#!/usr/bin/env python3
"""
启动 Streamlit 应用的脚本
"""

import subprocess
import sys
import os

def main():
    """启动 Streamlit 应用"""
    print("🚀 启动 Bo-Zh S2TT Demo...")
    
    # 检查是否在正确的目录
    if not os.path.exists("streamlit_app.py"):
        print("❌ 找不到 streamlit_app.py 文件")
        print("请确保在正确的目录中运行此脚本")
        return
    
    # 检查示例文件
    if not os.path.exists("examples"):
        print("⚠️  示例目录不存在，正在创建...")
        os.system("python create_examples.py")
    
    # 启动 Streamlit
    try:
        print("🌐 启动 Streamlit 服务器...")
        print("📱 应用将在浏览器中自动打开")
        print("🔗 如果没有自动打开，请访问: http://localhost:8501")
        print("⏹️  按 Ctrl+C 停止服务器")
        print("-" * 50)
        
        subprocess.run([
            sys.executable, "-m", "streamlit", "run", 
            "streamlit_app.py", 
            "--server.port", "8501"
        ])
    except KeyboardInterrupt:
        print("\n👋 应用已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")

if __name__ == "__main__":
    main()
