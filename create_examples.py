#!/usr/bin/env python3
"""
创建示例音频文件的脚本
生成简单的测试音频文件用于演示
"""

import os
import numpy as np
import soundfile as sf
from scipy import signal

def generate_sample_audio(filename, duration=3.0, sample_rate=16000):
    """
    生成示例音频文件
    
    Args:
        filename: 输出文件名
        duration: 音频时长（秒）
        sample_rate: 采样率
    """
    # 生成时间轴
    t = np.linspace(0, duration, int(sample_rate * duration), False)
    
    # 生成复合音频信号（模拟语音特征）
    # 基频和谐波
    f1, f2, f3 = 200, 400, 800  # 基频和谐波频率
    
    # 生成音频信号
    audio = (
        0.3 * np.sin(2 * np.pi * f1 * t) +  # 基频
        0.2 * np.sin(2 * np.pi * f2 * t) +  # 二次谐波
        0.1 * np.sin(2 * np.pi * f3 * t) +  # 三次谐波
        0.05 * np.random.randn(len(t))      # 添加少量噪声
    )
    
    # 应用包络（模拟语音的音量变化）
    envelope = signal.windows.hann(len(audio))
    audio = audio * envelope
    
    # 归一化到合适的音量
    audio = audio / np.max(np.abs(audio)) * 0.7
    
    # 保存为WAV文件
    sf.write(filename, audio, sample_rate)
    print(f"✅ 生成示例音频: {filename}")

def main():
    """主函数"""
    # 创建示例目录
    examples_dir = "examples"
    if not os.path.exists(examples_dir):
        os.makedirs(examples_dir)
        print(f"📁 创建目录: {examples_dir}")
    
    # 生成示例音频文件
    examples = [
        ("wz_sample.wav", "卫藏藏语示例"),
        ("kb_sample.wav", "康巴藏语示例"), 
        ("ad_sample.wav", "安多藏语示例")
    ]
    
    for filename, description in examples:
        filepath = os.path.join(examples_dir, filename)
        if not os.path.exists(filepath):
            print(f"🎵 生成 {description}...")
            generate_sample_audio(filepath)
        else:
            print(f"⏭️  跳过已存在的文件: {filepath}")
    
    print("\n🎉 示例音频文件创建完成！")
    print("现在可以在 Streamlit 应用中使用这些示例进行测试。")

if __name__ == "__main__":
    main()
