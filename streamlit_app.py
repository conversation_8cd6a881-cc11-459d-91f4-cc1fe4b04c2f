from infer import SpeechTranslator
import streamlit as st
import tempfile
import os

st.set_page_config(page_title="Bo-Zh S2TT Demo", layout='centered')
st.title("🎤 Bo-Zh S2TT Demo")

# 初始化会话状态和模型预加载
@st.cache_resource
def initialize_translators():
    """初始化并预加载所有语种的翻译模型"""
    translators = {}
    language_configs = ['ad', 'kb', 'wz', 'multi']

    for lang in language_configs:
        try:
            translator = SpeechTranslator()
            translator.load_model(lang)
            translators[lang] = translator
            st.success(f"✅ {lang.upper()} 模型加载成功")
        except Exception as e:
            st.warning(f"⚠ {lang.upper()} 模型加载失败: {str(e)}")
            translators[lang] = None

    return translators

# 初始化会话状态
if 'translators' not in st.session_state:
    st.session_state.translators = initialize_translators()
if 'translation_result' not in st.session_state:
    st.session_state.translation_result = ""
if 'reference_text' not in st.session_state:
    st.session_state.reference_text = ""

# 创建左右两列布局
st.markdown("---")
st.write("从本地上传：")
col1, col2 = st.columns(2)

with col1:
    # 第1行：文件上传
    uploaded_file = st.file_uploader(
        "📤 上传音频文件",
        type=["wav"],
        help="支持 WAV 格式的音频文件"
    )
    reference = None

with col2:
    # 第2行：麦克风录制
    audio_bytes = st.audio_input("🎙️麦克风录制")
    reference = None

# 示例数据选择区域
st.write("快速示例：")

# 定义示例数据
example_data = [
    {
        "name": "卫藏示例",
        "language": "wz",
        "filename": "/ws/wavs/(wzzw)1875751.wav",
        "reference": "违反武器使用规定",
        "description": "/ws/wavs/(wzzw)1875751.wav"
    },
    {
        "name": "康巴示例",
        "language": "kb",
        "filename": "/ws/wavs/(kbzw)1661995.wav",
        "reference": "更年期是一个医学术语。",
        "description": "/ws/wavs/(kbzw)1661995.wav"
    },
    {
        "name": "安多示例",
        "language": "ad",
        "filename": "/ws/wavs/(adzw)1686294.wav",
        "reference": "为后来的反法西斯斗争打下了坚实的基础。",
        "description": "/ws/wavs/(adzw)1686294.wav"
    }
]

# 创建示例选择界面
col_ex1, col_ex2, col_ex3 = st.columns(3)

selected_example = None
for i, example in enumerate(example_data):
    col = [col_ex1, col_ex2, col_ex3][i]
    with col:
        example_path = example["filename"]

        # 检查示例文件是否存在
        if os.path.exists(example_path):
            # file_status = "✅ 可用"
            button_disabled = False
        else:
            # file_status = "❌ 文件不存在"
            button_disabled = True

        st.markdown(f"{example['name']}")
        st.caption(f"{example['description']}")
        # st.caption(f"状态: {file_status}")

        if st.button(
            f"Try",
            key=f"example_{i}",
            disabled=button_disabled,
            use_container_width=True
        ):
            selected_example = example
            st.session_state.selected_lang = example["language"]
            st.session_state.selected_file = example_path
            st.session_state.selected_reference = example["reference"]
            st.rerun()

# 处理选中的示例
if selected_example:
    st.success(f"✅ 已选择: {selected_example['name']}")
    reference = selected_example["reference"]
elif hasattr(st.session_state, 'selected_reference'):
    reference = st.session_state.selected_reference
else:
    reference = None

# 添加清除选择按钮
if hasattr(st.session_state, 'selected_file') and st.session_state.selected_file:
    col_clear1, col_clear2, col_clear3 = st.columns([1, 1, 1])
    with col_clear2:
        if st.button("🗑️ 清除示例选择", use_container_width=True):
            # 清除所有示例相关的会话状态
            if hasattr(st.session_state, 'selected_lang'):
                delattr(st.session_state, 'selected_lang')
            if hasattr(st.session_state, 'selected_file'):
                delattr(st.session_state, 'selected_file')
            if hasattr(st.session_state, 'selected_reference'):
                delattr(st.session_state, 'selected_reference')
            st.rerun()


# 音频处理和播放区域
st.markdown("---")
# st.subheader("🎵 音频输入")

audio_path = None
audio_data = None
input_source = None

if uploaded_file is not None:
    # 处理上传的文件
    audio_data = uploaded_file.getvalue()
    with tempfile.NamedTemporaryFile(delete=False, suffix=".wav") as tmpfile:
        tmpfile.write(audio_data)
        audio_path = tmpfile.name
    input_source = "上传文件"
    st.audio(audio_data, format="audio/wav")

elif audio_bytes is not None:
    # 处理麦克风录制
    audio_data = audio_bytes
if audio_bytes is not None:
    with tempfile.NamedTemporaryFile(
        mode="wb",        # 必须显式二进制
        delete=False,
        suffix=".wav"
    ) as tmp:
        tmp.write(audio_bytes)
        
    with tempfile.NamedTemporaryFile(delete=False, suffix=".wav") as tmpfile:
        tmpfile.write(audio_data)
        audio_path = tmpfile.name
    input_source = "麦克风录制"
    st.audio(audio_data, format="audio/wav")
    st.success("✅ 录制完成")

elif hasattr(st.session_state, 'selected_file') and st.session_state.selected_file and os.path.exists(st.session_state.selected_file):
    # 处理选中的示例文件
    audio_path = st.session_state.selected_file
    input_source = "示例文件"

    # 显示音频播放器
    with open(st.session_state.selected_file, 'rb') as audio_file:
        audio_bytes = audio_file.read()
        st.audio(audio_bytes, format="audio/wav")

    # 显示示例信息
    if hasattr(st.session_state, 'selected_reference') and st.session_state.selected_reference:
        st.info(f"📝 参考译文: {st.session_state.selected_reference}")

    st.success("✅ 示例文件已加载")

# 显示当前输入状态
if audio_path:
    st.info(f"📍 当前音频源: {input_source}")

# 翻译控制区域
st.markdown("---")
col3, col4 = st.columns(2)
with col3:
    # 语种选择下拉菜单
    language_options = {
        'ad': '安多藏语 (AD)',
        'kb': '康巴藏语 (KB)',
        'wz': '卫藏藏语 (WZ)',
        'multi': '多语种模型 (MULTI)'
    }

    if hasattr(st.session_state, 'selected_lang') and st.session_state.selected_lang:
        selected_language = st.session_state.selected_lang
        st.selectbox(
            "选择语种:",
            options=list(language_options.keys()),
            format_func=lambda x: language_options[x],
            index=list(language_options.keys()).index(selected_language),
            disabled=True,
            help="已自动选择示例对应的语种"
        )
    else:
        selected_language = st.selectbox(
            "选择语种:",
            options=list(language_options.keys()),
            format_func=lambda x: language_options[x],
            index=0
        )

    # 显示模型加载状态
    if st.session_state.translators.get(selected_language):
        st.success(f"✅ {language_options[selected_language]} 已就绪")
    else:
        st.error(f"❌ {language_options[selected_language]} 加载失败")

with col4:
    submit_button = st.button(
        "🚀 开始翻译",
        type="primary",
        disabled=not audio_path,
        use_container_width=True
    )

# 处理翻译请求
if submit_button and audio_path:
    # 检查选择的模型是否可用
    if not st.session_state.translators.get(selected_language):
        st.error(f"❌ {language_options[selected_language]} 模型不可用，请检查模型文件")
    else:
        try:
            # 使用预加载的模型
            translator = st.session_state.translators[selected_language]

            # 执行翻译
            with st.spinner(f"正在使用 {language_options[selected_language]} 进行语音翻译..."):
                # 使用参考文本（如果有的话）
                ref_text = reference if reference else ""
                translation_result = translator.infer(audio_path, reference=ref_text)

                # 获取纯翻译文本
                clean_translation = translator.get_translation_only(audio_path, reference=ref_text)

                # 格式化结果
                if reference:
                    st.session_state.translation_result = f"参考译文: {reference}\n\n{translation_result}"
                else:
                    st.session_state.translation_result = translation_result

                # 保存纯翻译文本到会话状态
                st.session_state.clean_translation = clean_translation

                # 保存当前使用的参考文本
                st.session_state.current_reference = reference

                st.success("✅ 翻译完成!")

        except Exception as e:
            st.error(f"❌ 翻译过程中出现错误: {str(e)}")

# 显示翻译结果
if st.session_state.translation_result:
    st.markdown("---")
    # st.subheader("📝 翻译结果")

    # 显示纯翻译文本（如果有的话）
    if hasattr(st.session_state, 'clean_translation') and st.session_state.clean_translation:
        st.text_input(
            "模型译文:",
            value=st.session_state.clean_translation,
            help="仅包含翻译文本，便于复制使用"
        )

    # 显示详细翻译信息
    st.text_area(
        label="详细翻译信息",
        value=st.session_state.translation_result,
        height=200,
        help="包含评分和详细信息的完整翻译结果"
    )

# 页面底部信息
st.markdown("---")
st.markdown(
    """
    <div style='text-align: center; color: #666;'>
        <small> 语音翻译演示 | 支持安多藏语、康巴藏语、卫藏藏语及多语种模型 </small>
    </div>
    """,
    unsafe_allow_html=True
)
