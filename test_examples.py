#!/usr/bin/env python3
"""
测试示例数据功能的脚本
"""

import os
import soundfile as sf

def test_example_files():
    """测试示例文件是否正确创建"""
    examples_dir = "examples"
    expected_files = [
        "wz_sample.wav",
        "kb_sample.wav", 
        "ad_sample.wav"
    ]
    
    print("🔍 检查示例文件...")
    
    if not os.path.exists(examples_dir):
        print(f"❌ 示例目录不存在: {examples_dir}")
        return False
    
    all_good = True
    for filename in expected_files:
        filepath = os.path.join(examples_dir, filename)
        if os.path.exists(filepath):
            try:
                # 检查音频文件是否可以正常读取
                data, samplerate = sf.read(filepath)
                duration = len(data) / samplerate
                print(f"✅ {filename}: {duration:.2f}秒, {samplerate}Hz, {len(data)}样本")
            except Exception as e:
                print(f"❌ {filename}: 读取失败 - {e}")
                all_good = False
        else:
            print(f"❌ 文件不存在: {filepath}")
            all_good = False
    
    return all_good

def test_streamlit_imports():
    """测试 Streamlit 应用的导入"""
    print("\n🔍 检查 Streamlit 应用导入...")
    
    try:
        import streamlit as st
        print("✅ Streamlit 导入成功")
    except ImportError as e:
        print(f"❌ Streamlit 导入失败: {e}")
        return False
    
    try:
        from infer import SpeechTranslator
        print("✅ SpeechTranslator 导入成功")
    except ImportError as e:
        print(f"❌ SpeechTranslator 导入失败: {e}")
        return False
    
    return True

def main():
    """主测试函数"""
    print("🧪 开始测试示例数据功能...\n")
    
    # 测试示例文件
    files_ok = test_example_files()
    
    # 测试导入
    imports_ok = test_streamlit_imports()
    
    print("\n📊 测试结果:")
    print(f"示例文件: {'✅ 通过' if files_ok else '❌ 失败'}")
    print(f"模块导入: {'✅ 通过' if imports_ok else '❌ 失败'}")
    
    if files_ok and imports_ok:
        print("\n🎉 所有测试通过！可以启动 Streamlit 应用进行测试。")
        print("运行命令: streamlit run streamlit_app.py")
    else:
        print("\n⚠️  存在问题，请检查上述错误信息。")

if __name__ == "__main__":
    main()
